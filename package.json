{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "nuxt start"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@mdi/font": "^7.4.47", "@prisma/client": "^6.10.1", "mammoth": "^1.9.1", "nuxt": "^3.17.5", "pdfjs-dist": "^5.3.31", "sass": "^1.89.2", "uuid": "^11.1.0", "vue": "^3.5.16", "vue-router": "^4.5.1", "vuetify": "^3.8.10"}, "devDependencies": {"@nuxtjs/i18n": "^9.5.5", "@nuxtjs/tailwindcss": "^7.0.0-beta.0", "@tailwindcss/forms": "^0.5.10", "@types/pdfjs-dist": "^2.10.377", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "prisma": "^6.10.1", "tailwindcss": "^4.1.11", "vue-tsc": "^2.2.10"}}