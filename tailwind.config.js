/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    // Only include admin pages and components
    './pages/admin/**/*.{js,vue,ts}',
    './components/admin/**/*.{js,vue,ts}',
    './layouts/admin.vue'
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
  // Ensure Tailwind only applies to admin pages
  corePlugins: {
    preflight: false, // Disable global reset to avoid conflicts
  }
}
