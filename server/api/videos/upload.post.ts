import { promises as fs } from 'fs';
import path from 'path';

export default defineEventHandler(async (event) => {
  try {
    const form = await readMultipartFormData(event);
    
    if (!form || form.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No files uploaded'
      });
    }

    const uploadedFiles = [];
    const publicDir = path.join(process.cwd(), 'public');

    for (const file of form) {
      if (file.name === 'file' && file.filename && file.data) {
        // Get file extension
        const ext = path.extname(file.filename);
        const allowedExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv'];
        
        if (!allowedExtensions.includes(ext.toLowerCase())) {
          throw createError({
            statusCode: 400,
            statusMessage: `File type ${ext} not allowed. Allowed types: ${allowedExtensions.join(', ')}`
          });
        }

        // Generate new filename or use provided name
        const originalName = path.parse(file.filename).name;
        const newFilename = `${originalName}-${Date.now()}${ext}`;
        const filePath = path.join(publicDir, 'videos', newFilename);

        // Ensure videos directory exists
        await fs.mkdir(path.dirname(filePath), { recursive: true });

        // Write file
        await fs.writeFile(filePath, file.data);

        uploadedFiles.push({
          originalName: file.filename,
          newName: newFilename,
          path: `/videos/${newFilename}`,
          size: file.data.length,
          type: file.type || 'unknown'
        });
      }
    }

    return {
      success: true,
      files: uploadedFiles
    };
  } catch (error) {
    console.error('Error uploading videos:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to upload videos'
    });
  }
});
