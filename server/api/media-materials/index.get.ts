import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const page = parseInt(query.page as string) || 1
    const pageSize = parseInt(query.pageSize as string) || 10
    const locale = query.locale as string || 'en'
    const type = query.type as string // 'video', 'image', 'pdf'
    const published = query.published !== 'false'

    const whereClause: any = {
      locale,
      published
    }

    if (type) {
      whereClause.type = type
    }

    // Get total count
    const total = await prisma.mediaMaterial.count({
      where: whereClause
    })

    // Get paginated media materials
    const materials = await prisma.mediaMaterial.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      skip: (page - 1) * pageSize,
      take: pageSize
    })

    return {
      success: true,
      data: materials,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  } catch (error: any) {
    console.error('Failed to fetch media materials:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch media materials'
    })
  } finally {
    await prisma.$disconnect()
  }
})
