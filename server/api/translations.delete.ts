export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { key } = body

    if (!key) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Translation key is required'
      })
    }

    // For now, return a mock response since Prisma client needs to be generated
    // TODO: Implement actual database deletion once Prisma is properly set up
    console.log(`Would delete translations for key: ${key}`)

    return {
      success: true,
      message: `Would delete translations for key: ${key}`,
      deletedCount: 3 // Mock count for 3 locales
    }
  } catch (error: any) {
    console.error('Failed to delete translation key:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to delete translation key'
    })
  }
})
