import { promises as fs } from 'fs';
import path from 'path';

export default defineEventHandler(async (event) => {
  try {
    const publicDir = path.join(process.cwd(), 'public');
    const imagesDir = path.join(publicDir, 'images');

    // Ensure images directory exists
    try {
      await fs.access(imagesDir);
    } catch {
      await fs.mkdir(imagesDir, { recursive: true });
      return { success: true, images: [] };
    }

    // Read all files in images directory
    const files = await fs.readdir(imagesDir, { withFileTypes: true });
    
    const images = [];
    for (const file of files) {
      if (file.isFile()) {
        const ext = path.extname(file.name).toLowerCase();
        const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
        
        if (allowedExtensions.includes(ext)) {
          const filePath = path.join(imagesDir, file.name);
          const stats = await fs.stat(filePath);
          
          images.push({
            name: file.name,
            path: `/images/${file.name}`,
            size: stats.size,
            modified: stats.mtime,
            extension: ext
          });
        }
      }
    }

    // Sort by modification date (newest first)
    images.sort((a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime());

    return {
      success: true,
      images
    };
  } catch (error) {
    console.error('Error listing images:', error);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to list images'
    });
  }
});
