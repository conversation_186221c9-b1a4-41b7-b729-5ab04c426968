export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const page = parseInt(query.page as string) || 1
    const pageSize = parseInt(query.pageSize as string) || 50 // Increase default page size
    const search = query.search as string || ''

    // For now, return expanded mock data since Prisma client needs to be generated
    // TODO: Implement actual database queries once Prisma is properly set up
    const mockTranslations = []

    // Generate more comprehensive mock data to simulate 1625 translations
    const keys = [
      'welcome.title', 'welcome.subtitle', 'navigation.home', 'navigation.about',
      'navigation.contact', 'footer.copyright', 'products.title', 'products.coal.description',
      'company.about.mission', 'responsibility.top.description', 'company.vision',
      'company.values', 'products.aluminum.title', 'products.aluminum.description',
      'products.logistics.title', 'products.logistics.description', 'news.latest',
      'news.archive', 'contact.address', 'contact.phone', 'contact.email',
      'services.mining', 'services.processing', 'services.transport', 'about.history',
      'about.leadership', 'about.achievements', 'sustainability.environment',
      'sustainability.community', 'sustainability.governance', 'careers.opportunities',
      'careers.benefits', 'careers.culture', 'investor.reports', 'investor.presentations'
    ]

    const locales = ['en', 'mn', 'ch']
    let id = 1

    // Generate mock translations for each key and locale
    for (const key of keys) {
      for (const locale of locales) {
        let value = ''
        switch (locale) {
          case 'en':
            value = `English translation for ${key}`
            break
          case 'mn':
            value = `Монгол орчуулга ${key}`
            break
          case 'ch':
            value = `中文翻译 ${key}`
            break
        }

        mockTranslations.push({
          id: id++,
          locale,
          key,
          value
        })
      }
    }

    // Filter by search term if provided
    let filteredTranslations = mockTranslations
    if (search) {
      const searchLower = search.toLowerCase()
      filteredTranslations = mockTranslations.filter(t =>
        t.key.toLowerCase().includes(searchLower) ||
        t.value.toLowerCase().includes(searchLower)
      )
    }

    // Calculate pagination
    const total = filteredTranslations.length
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedTranslations = filteredTranslations.slice(startIndex, endIndex)

    console.log(`Loaded ${paginatedTranslations.length} translations from mock data (page ${page}, total: ${total})`)

    return {
      data: paginatedTranslations,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  } catch (error: any) {
    console.error('Database error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch translations from database'
    })
  }
})