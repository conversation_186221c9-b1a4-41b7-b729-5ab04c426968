import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const page = parseInt(query.page as string) || 1
    const pageSize = parseInt(query.pageSize as string) || 50 // Increase default page size
    const search = query.search as string || ''

    // Build where clause for search
    const whereClause = search ? {
      OR: [
        { key: { contains: search, mode: 'insensitive' } },
        { value: { contains: search, mode: 'insensitive' } }
      ]
    } : {}

    // Get total count
    const total = await prisma.translation.count({
      where: whereClause
    })

    // Get paginated translations
    const translations = await prisma.translation.findMany({
      where: whereClause,
      orderBy: [
        { key: 'asc' },
        { locale: 'asc' }
      ],
      skip: (page - 1) * pageSize,
      take: pageSize
    })

    console.log(`Loaded ${translations.length} translations from database (page ${page}, total: ${total})`)

    return {
      data: translations,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    }
  } catch (error) {
    console.error('Database error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch translations from database'
    })
  } finally {
    await prisma.$disconnect()
  }
})