// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Translation {
  id     Int    @id @default(autoincrement())
  locale String
  key    String
  value  String

  @@unique([locale, key], name: "locale_key")
}

model News {
  id          Int        @id @default(autoincrement())
  slug        String     @unique
  title       String
  description String
  content     String?
  image       String?
  date        DateTime   @default(now())
  source      String?
  views       Int        @default(0)
  published   Boolean    @default(true)
  locale      String     @default("en")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  files       NewsFile[]

  @@index([slug])
  @@index([locale])
  @@index([published])
  @@index([date])
}

model NewsFile {
  id           Int    @id @default(autoincrement())
  newsId       Int
  filename     String
  originalName String
  path         String
  size         Int
  mimeType     String
  createdAt    DateTime @default(now())
  news         News   @relation(fields: [newsId], references: [id], onDelete: Cascade)
}

model MediaMaterial {
  id            Int      @id @default(autoincrement())
  title         String
  description   String?
  type          String   // 'video', 'image', 'pdf'
  filename      String
  originalName  String
  path          String
  thumbnailPath String?
  size          Int
  mimeType      String
  duration      Int?     // for videos in seconds
  dimensions    String?  // for images/videos "1920x1080"
  published     Boolean  @default(true)
  locale        String   @default("en")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([type])
  @@index([locale])
  @@index([published])
}
