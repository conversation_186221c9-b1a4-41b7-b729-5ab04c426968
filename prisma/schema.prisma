// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Translation {
  id     Int    @id @default(autoincrement())
  locale String
  key    String
  value  String

  @@unique([locale, key], name: "locale_key")
}

model News {
  id          Int        @id @default(autoincrement())
  slug        String     @unique
  title       String
  description String
  content     String?
  image       String?
  date        DateTime   @default(now())
  source      String?
  views       Int        @default(0)
  published   Boolean    @default(true)
  locale      String     @default("en")
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  files       NewsFile[]

  @@index([slug])
  @@index([locale])
  @@index([published])
  @@index([date])
}

model NewsFile {
  id           Int    @id @default(autoincrement())
  newsId       Int
  filename     String
  originalName String
  path         String
  size         Int
  mimeType     String
  createdAt    DateTime @default(now())
  news         News   @relation(fields: [newsId], references: [id], onDelete: Cascade)
}

model MediaMaterial {
  id            Int      @id @default(autoincrement())
  title         String
  description   String?
  type          String   // 'video', 'image', 'pdf'
  filename      String
  originalName  String
  path          String
  thumbnailPath String?
  size          Int
  mimeType      String
  duration      Int?     // for videos in seconds
  dimensions    String?  // for images/videos "1920x1080"
  published     Boolean  @default(true)
  locale        String   @default("en")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([type])
  @@index([locale])
  @@index([published])
}

model JoinUs {
  id          Int      @id @default(autoincrement())
  slug        String   @unique
  title       String
  description String?
  content     String?
  type        String   // 'social', 'campus', 'staff-style'
  category    String?  // job category for recruitment
  location    String?  // job location
  salary      String?  // salary range
  contact     String?  // contact person
  contactNumber String? // contact phone
  requirements String? // job requirements (JSON string)
  responsibilities String? // job responsibilities (JSON string)
  image       String?
  published   Boolean  @default(true)
  locale      String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([locale])
  @@index([published])
  @@index([slug])
}

model PartyBuilding {
  id          Int      @id @default(autoincrement())
  slug        String   @unique
  title       String
  description String?
  content     String?  // JSON string for paragraphs
  mainImage   String?
  contentImages String? // JSON array of image paths
  date        DateTime @default(now())
  source      String?
  views       Int      @default(0)
  published   Boolean  @default(true)
  locale      String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([slug])
  @@index([locale])
  @@index([published])
  @@index([date])
}

model DevelopmentHistory {
  id          Int      @id @default(autoincrement())
  year        Int
  title       String
  description String
  achievements String? // JSON array of achievement items
  published   Boolean  @default(true)
  locale      String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([year])
  @@index([locale])
  @@index([published])
  @@unique([year, locale], name: "year_locale")
}

model CollaborativeProject {
  id          Int      @id @default(autoincrement())
  slug        String   @unique
  title       String
  description String?
  content     String?
  type        String   // 'new-materials', 'new-energy', 'fly-ash', 'cooperation'
  image       String?
  contactEmail String?
  contactPhone String?
  contactName String?
  address     String?
  published   Boolean  @default(true)
  locale      String   @default("en")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([locale])
  @@index([published])
  @@index([slug])
}
