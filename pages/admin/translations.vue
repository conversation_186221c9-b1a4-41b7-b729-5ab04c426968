<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
          Content Management Studio
        </h1>
        <p class="text-lg text-gray-600 max-w-2xl mx-auto">
          Manage translations, images, and videos with live preview and advanced editing tools
        </p>
      </div>

      <!-- Tab Navigation -->
      <div class="bg-white rounded-2xl shadow-xl border border-gray-200 mb-8">
        <div class="border-b border-gray-200">
          <nav class="flex space-x-8 px-6" aria-label="Tabs">
            <button
              @click="activeTab = 'translations'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200',
                activeTab === 'translations'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                </svg>
                <span>Translations</span>
                <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">{{ totalItems || 0 }}</span>
              </div>
            </button>

            <button
              @click="activeTab = 'images'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200',
                activeTab === 'images'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <span>Images</span>
                <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">{{ images.length }}</span>
              </div>
            </button>

            <button
              @click="activeTab = 'videos'"
              :class="[
                'py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200',
                activeTab === 'videos'
                  ? 'border-purple-500 text-purple-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                <span>Videos</span>
                <span class="bg-purple-100 text-purple-600 text-xs px-2 py-1 rounded-full">{{ videos.length }}</span>
              </div>
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
          <!-- Translations Tab -->
          <div v-if="activeTab === 'translations'" class="space-y-6">
            <!-- Search & Controls -->
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div class="relative flex-grow max-w-md">
                <input
                  v-model="searchKey"
                  @input="page = 1; loadTranslations()"
                  placeholder="Search translation keys..."
                  class="w-full px-4 py-3 pl-11 pr-10 border border-gray-300 rounded-xl text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 focus:bg-white transition-all duration-200"
                >
                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <div v-if="loading" class="absolute inset-y-0 right-10 flex items-center">
                  <svg class="animate-spin h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
                <button
                  v-if="searchKey"
                  @click="searchKey = ''; page = 1; loadTranslations()"
                  class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>

              <div class="flex flex-wrap gap-3">
                <button
                  @click="showPreview = !showPreview"
                  :class="[
                    'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                    showPreview
                      ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/25'
                      : 'bg-white text-blue-600 border border-blue-200 hover:bg-blue-50'
                  ]"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <span>{{ showPreview ? 'Hide Preview' : 'Show Preview' }}</span>
                </button>

                <button
                  @click="exportTranslations"
                  class="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl text-sm font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <span>Export JSON</span>
                </button>
              </div>
            </div>

            <!-- Main Content Area -->
            <div class="flex flex-col xl:flex-row gap-8">
              <!-- Translations Editor -->
              <div class="flex-grow w-full xl:w-2/3">
                <div class="space-y-6">
            <div v-for="key in filteredKeys" :key="key"
                 class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">

              <!-- Key Header -->
              <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <h3 class="font-semibold text-gray-800 truncate">{{ key }}</h3>
                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {{ filteredKeys.indexOf(key) + 1 }}/{{ filteredKeys.length }}
                    </span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <button
                      v-if="keyUsageInfo[key]"
                      @click="toggleContextPreview(key)"
                      class="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      {{ showContextPreviewFor === key ? 'Hide Preview' : 'Show Preview' }}
                    </button>
                    <button
                      @click="copyTranslationKey(key)"
                      class="px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                      title="Copy key to clipboard"
                    >
                      Copy Key
                    </button>
                  </div>
                </div>

                <!-- Usage Info -->
                <div v-if="keyUsageInfo[key]" class="mt-2 text-xs text-gray-600">
                  <span class="inline-flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Used in: <strong>{{ keyUsageInfo[key].page }}</strong>
                  </span>
                  <span class="ml-4 inline-flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                    Component: <strong>{{ keyUsageInfo[key].component }}</strong>
                  </span>
                </div>
              </div>

              <!-- Live Context Preview -->
              <div v-if="showContextPreviewFor === key && keyUsageInfo[key]" class="border-b border-gray-200">
                <div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
                  <span class="text-sm font-medium text-gray-700">Live Preview</span>
                  <div class="flex gap-1">
                    <button
                      @click="contextPreviewLocale = 'en'"
                      class="px-2 py-1 rounded text-xs transition-colors"
                      :class="contextPreviewLocale === 'en' ? 'bg-blue-500 text-white' : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'"
                    >🇺🇸 EN</button>
                    <button
                      @click="contextPreviewLocale = 'mn'"
                      class="px-2 py-1 rounded text-xs transition-colors"
                      :class="contextPreviewLocale === 'mn' ? 'bg-yellow-500 text-white' : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'"
                    >🇲🇳 MN</button>
                    <button
                      @click="contextPreviewLocale = 'ch'"
                      class="px-2 py-1 rounded text-xs transition-colors"
                      :class="contextPreviewLocale === 'ch' ? 'bg-red-500 text-white' : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'"
                    >🇨🇳 CH</button>
                  </div>
                </div>
                <div class="relative bg-white">
                  <iframe
                    :src="`/iframe-preview/${keyUsageInfo[key].page}?locale=${contextPreviewLocale}&key=${key}&value=${encodeURIComponent(getTranslationValue(key, contextPreviewLocale))}`"
                    class="w-full h-64 border-none"
                    :key="`${key}-${contextPreviewLocale}-${getTranslationValue(key, contextPreviewLocale)}`"
                  ></iframe>
                  <div class="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 rounded text-xs">
                    Preview: {{ contextPreviewLocale.toUpperCase() }}
                  </div>
                </div>
              </div>
              
              <!-- Translation Editor -->
              <div class="p-6">
                <div class="space-y-6">
                  <!-- English Translation -->
                  <div class="translation-group">
                    <div class="flex justify-between items-center mb-3">
                      <div class="flex items-center space-x-3">
                        <span class="px-3 py-1 bg-blue-500 text-white rounded-full text-sm font-medium">🇺🇸 EN</span>
                        <span class="text-gray-700 font-medium">English</span>
                        <span class="text-xs text-gray-500">{{ getTranslation(key, 'en').value?.length || 0 }} chars</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span v-if="getTranslation(key, 'en')?.updatedAt" class="text-green-600 text-xs flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          Saved
                        </span>
                        <button
                          @click="updateTranslation(getTranslation(key, 'en'))"
                          class="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
                          :disabled="!getTranslation(key, 'en').value"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                    <textarea
                      v-model="getTranslation(key, 'en').value"
                      class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical text-sm min-h-[80px]"
                      placeholder="Enter English translation..."
                      @input="onTranslationChange(key, 'en')"
                    ></textarea>
                  </div>

                  <!-- Mongolian Translation -->
                  <div class="translation-group">
                    <div class="flex justify-between items-center mb-3">
                      <div class="flex items-center space-x-3">
                        <span class="px-3 py-1 bg-yellow-500 text-white rounded-full text-sm font-medium">🇲🇳 MN</span>
                        <span class="text-gray-700 font-medium">Mongolian</span>
                        <span class="text-xs text-gray-500">{{ getTranslation(key, 'mn').value?.length || 0 }} chars</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span v-if="getTranslation(key, 'mn')?.updatedAt" class="text-green-600 text-xs flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          Saved
                        </span>
                        <button
                          @click="updateTranslation(getTranslation(key, 'mn'))"
                          class="px-2 py-1 bg-yellow-500 text-white text-xs rounded hover:bg-yellow-600 transition-colors"
                          :disabled="!getTranslation(key, 'mn').value"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                    <textarea
                      v-model="getTranslation(key, 'mn').value"
                      class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 resize-vertical text-sm min-h-[80px]"
                      placeholder="Enter Mongolian translation..."
                      @input="onTranslationChange(key, 'mn')"
                    ></textarea>
                  </div>

                  <!-- Chinese Translation -->
                  <div class="translation-group">
                    <div class="flex justify-between items-center mb-3">
                      <div class="flex items-center space-x-3">
                        <span class="px-3 py-1 bg-red-500 text-white rounded-full text-sm font-medium">🇨🇳 CH</span>
                        <span class="text-gray-700 font-medium">Chinese</span>
                        <span class="text-xs text-gray-500">{{ getTranslation(key, 'ch').value?.length || 0 }} chars</span>
                      </div>
                      <div class="flex items-center space-x-2">
                        <span v-if="getTranslation(key, 'ch')?.updatedAt" class="text-green-600 text-xs flex items-center">
                          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                          Saved
                        </span>
                        <button
                          @click="updateTranslation(getTranslation(key, 'ch'))"
                          class="px-2 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
                          :disabled="!getTranslation(key, 'ch').value"
                        >
                          Save
                        </button>
                      </div>
                    </div>
                    <textarea
                      v-model="getTranslation(key, 'ch').value"
                      class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-vertical text-sm min-h-[80px]"
                      placeholder="Enter Chinese translation..."
                      @input="onTranslationChange(key, 'ch')"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="filteredKeys.length === 0" class="bg-white rounded-2xl shadow-lg p-12 text-center">
            <div class="text-6xl mb-4 opacity-75">🔍</div>
            <h3 class="text-2xl font-bold text-gray-800 mb-2">No translations found</h3>
            <p class="text-gray-600 max-w-md mx-auto">Try adjusting your search terms or check if translations exist in the database.</p>
          </div>
          
          <!-- Pagination -->
          <div v-if="filteredKeys.length > 0" class="mt-8">
            <div class="bg-gradient-to-r from-white via-blue-50 to-white rounded-2xl shadow-xl border border-gray-200 p-6">
              <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
                <!-- Results Info -->
                <div class="flex items-center space-x-4">
                  <div class="text-sm text-gray-600 bg-white px-4 py-2 rounded-xl border border-gray-200 shadow-sm">
                    <span class="font-medium text-blue-600">{{ ((page - 1) * pageSize) + 1 }}-{{ Math.min(page * pageSize, totalItems) }}</span>
                    <span class="text-gray-500 mx-1">of</span>
                    <span class="font-medium text-blue-600">{{ totalItems }}</span>
                    <span class="text-gray-500 ml-1">translations</span>
                  </div>

                  <!-- Page Size Selector -->
                  <div class="flex items-center space-x-2">
                    <label class="text-sm text-gray-600 font-medium">Show:</label>
                    <select
                      v-model="pageSize"
                      @change="page = 1; loadTranslations()"
                      class="border border-gray-300 rounded-xl px-3 py-2 text-sm bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
                    >
                      <option :value="10">10</option>
                      <option :value="25">25</option>
                      <option :value="50">50</option>
                      <option :value="100">100</option>
                    </select>
                  </div>
                </div>

                <!-- Pagination Controls -->
                <div class="flex items-center space-x-2">
                  <!-- First Page -->
                  <button
                    @click="page = 1; loadTranslations()"
                    :disabled="page === 1"
                    :class="[
                      'p-2 rounded-xl text-sm font-medium transition-all duration-200',
                      page === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 shadow-sm hover:shadow-md'
                    ]"
                    title="First page"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <!-- Previous Page -->
                  <button
                    @click="page = Math.max(1, page - 1); loadTranslations()"
                    :disabled="page === 1"
                    :class="[
                      'p-2 rounded-xl text-sm font-medium transition-all duration-200',
                      page === 1
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 shadow-sm hover:shadow-md'
                    ]"
                    title="Previous page"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                  </button>

                  <!-- Current Page Display -->
                  <div class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-xl text-sm font-bold shadow-lg shadow-blue-500/25">
                    {{ page }} / {{ totalPages || 1 }}
                  </div>

                  <!-- Next Page -->
                  <button
                    @click="page = Math.min(totalPages, page + 1); loadTranslations()"
                    :disabled="page >= totalPages"
                    :class="[
                      'p-2 rounded-xl text-sm font-medium transition-all duration-200',
                      page >= totalPages
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 shadow-sm hover:shadow-md'
                    ]"
                    title="Next page"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                  </button>

                  <!-- Last Page -->
                  <button
                    @click="page = totalPages; loadTranslations()"
                    :disabled="page >= totalPages"
                    :class="[
                      'p-2 rounded-xl text-sm font-medium transition-all duration-200',
                      page >= totalPages
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-white text-gray-700 border border-gray-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 shadow-sm hover:shadow-md'
                    ]"
                    title="Last page"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

            <!-- Preview Panel -->
            <div v-if="showPreview" class="w-full xl:w-1/3">
              <div class="bg-white rounded-2xl shadow-xl border border-gray-200 sticky top-4 overflow-hidden">
                <!-- Preview Header -->
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 p-4 text-white">
                  <h2 class="text-lg font-semibold flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    Live Preview
                  </h2>
                </div>

                <!-- Language Selector -->
                <div class="p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-blue-50">
                  <div class="flex gap-2">
                    <button
                      @click="previewLocale = 'en'"
                      :class="[
                        'flex-1 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200',
                        previewLocale === 'en'
                          ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/25'
                          : 'bg-white border border-gray-300 text-gray-700 hover:bg-blue-50'
                      ]"
                    >
                      🇺🇸 EN
                    </button>
                    <button
                      @click="previewLocale = 'mn'"
                      :class="[
                        'flex-1 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200',
                        previewLocale === 'mn'
                          ? 'bg-yellow-500 text-white shadow-lg shadow-yellow-500/25'
                          : 'bg-white border border-gray-300 text-gray-700 hover:bg-yellow-50'
                      ]"
                    >
                      🇲🇳 MN
                    </button>
                    <button
                      @click="previewLocale = 'ch'"
                      :class="[
                        'flex-1 px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200',
                        previewLocale === 'ch'
                          ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                          : 'bg-white border border-gray-300 text-gray-700 hover:bg-red-50'
                      ]"
                    >
                      🇨🇳 CH
                    </button>
                  </div>
                </div>

                <!-- Preview Content -->
                <div class="max-h-[calc(100vh-16rem)] overflow-auto">
                  <div class="p-4 space-y-3">
                    <div v-for="key in filteredKeys.slice(0, 20)" :key="key" class="preview-item bg-gradient-to-r from-white to-gray-50 rounded-xl p-3 border border-gray-100 hover:shadow-md transition-all duration-200">
                      <div class="flex items-start justify-between mb-2">
                        <div class="text-xs font-mono text-gray-500 truncate flex-1 mr-2 bg-gray-100 px-2 py-1 rounded">{{ key }}</div>
                        <button
                          @click="copyToClipboard(getTranslationValue(key, previewLocale))"
                          class="text-xs text-gray-400 hover:text-blue-600 p-1 rounded hover:bg-blue-50 transition-all duration-200"
                          title="Copy translation"
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                          </svg>
                        </button>
                      </div>
                      <div class="text-sm text-gray-800 leading-relaxed">
                        {{ getTranslationValue(key, previewLocale) || '(No translation)' }}
                      </div>
                    </div>

                    <div v-if="filteredKeys.length > 20" class="text-center py-4 text-gray-500 text-sm bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                      ... and {{ filteredKeys.length - 20 }} more translations
                    </div>

                    <div v-if="filteredKeys.length === 0" class="text-center py-8 text-gray-500">
                      <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6">
                        <svg class="mx-auto h-8 w-8 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p class="text-sm font-medium">No translations to preview</p>
                        <p class="text-xs text-gray-400 mt-1">Try adjusting your search terms</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="activeTab === 'images'" class="space-y-6">
            <!-- Images Tab -->
            <!-- Images Header & Controls -->
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Image Gallery</h3>
                <p class="text-sm text-gray-600">Upload and manage your website images</p>
              </div>

              <div class="flex gap-3">
                <input
                  ref="fileInput"
                  type="file"
                  multiple
                  accept="image/*"
                  @change="handleImageUpload"
                  class="hidden"
                >
                <button
                  @click="$refs.fileInput?.click()"
                  class="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl text-sm font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-green-500/25"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <span>Upload Images</span>
                </button>
                <button
                  @click="loadImages"
                  class="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-xl text-sm font-medium hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  <span>Refresh</span>
                </button>
              </div>
            </div>

            <!-- Images Grid -->
            <div v-if="images.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div
                v-for="image in images"
                :key="image.name"
                class="group relative bg-white rounded-2xl overflow-hidden border border-gray-200 hover:shadow-xl hover:shadow-green-500/10 transition-all duration-300 cursor-pointer"
                @click="selectImage(image)"
              >
                <div class="aspect-square overflow-hidden">
                  <img
                    :src="image.path"
                    :alt="image.name"
                    class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  >
                </div>
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div class="absolute bottom-0 left-0 right-0 p-3 text-white">
                    <div class="text-xs font-medium truncate">{{ image.name }}</div>
                    <div class="text-xs opacity-75">{{ formatFileSize(image.size) }}</div>
                  </div>
                </div>
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <button
                    @click.stop="copyToClipboard(image.path)"
                    class="p-1.5 bg-white/90 backdrop-blur-sm rounded-lg text-gray-700 hover:bg-white transition-colors"
                    title="Copy image path"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-12">
              <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-3xl p-8 max-w-md mx-auto">
                <svg class="mx-auto h-16 w-16 text-green-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No images yet</h3>
                <p class="text-gray-600 mb-4">Upload your first images to get started with your gallery</p>
                <button
                  @click="$refs.fileInput?.click()"
                  class="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl font-medium hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg shadow-green-500/25"
                >
                  Upload Images
                </button>
              </div>
            </div>
          </div>
          <div v-else-if="activeTab === 'videos'" class="space-y-6">
            <!-- Videos Tab -->
            <!-- Videos Header & Controls -->
            <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div>
                <h3 class="text-lg font-semibold text-gray-900">Video Library</h3>
                <p class="text-sm text-gray-600">Upload and manage your website videos</p>
              </div>

              <div class="flex gap-3">
                <input
                  ref="videoInput"
                  type="file"
                  multiple
                  accept="video/*"
                  @change="handleVideoUpload"
                  class="hidden"
                >
                <button
                  @click="$refs.videoInput?.click()"
                  class="px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl text-sm font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 flex items-center space-x-2 shadow-lg shadow-purple-500/25"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                  </svg>
                  <span>Upload Videos</span>
                </button>
                <button
                  @click="loadVideos"
                  class="px-4 py-2 bg-white text-gray-700 border border-gray-300 rounded-xl text-sm font-medium hover:bg-gray-50 transition-all duration-200 flex items-center space-x-2"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  <span>Refresh</span>
                </button>
              </div>
            </div>

            <!-- Videos Grid -->
            <div v-if="videos.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <div
                v-for="video in videos"
                :key="video.name"
                class="group relative bg-white rounded-2xl overflow-hidden border border-gray-200 hover:shadow-xl hover:shadow-purple-500/10 transition-all duration-300"
              >
                <div class="aspect-video bg-gray-100 flex items-center justify-center">
                  <video
                    :src="video.path"
                    class="w-full h-full object-cover"
                    controls
                    preload="metadata"
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div class="p-4">
                  <div class="flex items-start justify-between">
                    <div class="flex-1 min-w-0">
                      <h4 class="text-sm font-medium text-gray-900 truncate">{{ video.name }}</h4>
                      <p class="text-xs text-gray-500 mt-1">{{ formatFileSize(video.size) }}</p>
                    </div>
                    <button
                      @click="copyToClipboard(video.path)"
                      class="ml-2 p-1.5 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200"
                      title="Copy video path"
                    >
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-12">
              <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-3xl p-8 max-w-md mx-auto">
                <svg class="mx-auto h-16 w-16 text-purple-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">No videos yet</h3>
                <p class="text-gray-600 mb-4">Upload your first videos to get started with your library</p>
                <button
                  @click="$refs.videoInput?.click()"
                  class="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg shadow-purple-500/25"
                >
                  Upload Videos
                </button>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

const activeTab = ref('translations')
const searchKey = ref('')
const translations = ref([])
const loading = ref(false)
const showPreview = ref(false)
const previewLocale = ref('en')
const contextPreviewLocale = ref('en')
const showContextPreviewFor = ref(null)
const images = ref([])
const videos = ref([])
const selectedImage = ref(null)

// Enhanced usage info with more details
const keyUsageInfo = ref({
  'responsibility.top.description': {
    page: 'responsibility-monte',
    component: 'HeroBanner',
    element: '.hero-description',
    file: 'pages/responsibility-monte/index.vue'
  },
  'groupWebsiteGroup': {
    page: 'index',
    component: 'Navigation',
    element: '.nav-item',
    file: 'layouts/AppHeader.vue'
  },
  'staffChannel': {
    page: 'index',
    component: 'Navigation',
    element: '.nav-item',
    file: 'layouts/AppHeader.vue'
  },
  'chinese': {
    page: 'index',
    component: 'LanguageSelector',
    element: '.language-option',
    file: 'components/LanguageSelector.vue'
  },
  'english': {
    page: 'index',
    component: 'LanguageSelector',
    element: '.language-option',
    file: 'components/LanguageSelector.vue'
  },
  'mongolian': {
    page: 'index',
    component: 'LanguageSelector',
    element: '.language-option',
    file: 'components/LanguageSelector.vue'
  }
})

// Get query parameters
const route = useRoute()
const highlightKey = route.query.key

// Pagination and search state
const page = ref(1)
const pageSize = ref(10)
const totalPages = ref(1)
const totalItems = ref(0)

// Load translations with pagination and search
async function loadTranslations() {
  loading.value = true
  try {
    const response = await $fetch('/api/translations', {
      params: {
        page: page.value,
        pageSize: pageSize.value,
        search: searchKey.value
      }
    })

    if (response && response.data) {
      translations.value = response.data
      totalPages.value = response.pagination?.totalPages || 1
      totalItems.value = response.pagination?.total || 0
      console.log('Loaded translations:', translations.value.length)
    } else {
      console.warn('No data received from API')
      translations.value = []
    }
  } catch (error) {
    console.error('Failed to load translations:', error)
    translations.value = []
  } finally {
    loading.value = false
  }
}

// Initial load
await loadTranslations()

// If a key is specified in the URL, set the search to that key
if (highlightKey) {
  searchKey.value = String(highlightKey)
}

// Toggle context preview for a specific key
function toggleContextPreview(key) {
  if (showContextPreviewFor.value === key) {
    showContextPreviewFor.value = null
  } else {
    showContextPreviewFor.value = key
  }
}

// Get unique keys
const uniqueKeys = computed(() => {
  const keys = new Set()
  translations.value.forEach(t => keys.add(t.key))
  return Array.from(keys)
})

// Filter keys based on search
const filteredKeys = computed(() => {
  if (!searchKey.value) return uniqueKeys.value
  return uniqueKeys.value.filter(key => 
    key.toLowerCase().includes(searchKey.value.toLowerCase())
  )
})

// Watch for search key changes from URL
watch(() => route.query.key, (newKey) => {
  if (newKey && newKey !== searchKey.value) {
    searchKey.value = String(newKey)
    page.value = 1
    loadTranslations()
  }
})

// Get translation for a specific key and locale
function getTranslation(key, locale) {
  const found = translations.value.find(t => t.key === key && t.locale === locale)
  if (found) {
    return found
  }
  // Return a default object with the key structure for new translations
  return {
    id: null,
    key,
    locale,
    value: '',
    updatedAt: null
  }
}

// Get translation value for preview
function getTranslationValue(key, locale) {
  const translation = getTranslation(key, locale)
  return translation.value || ''
}

// Update translation
async function updateTranslation(translation) {
  if (!translation || !translation.id) return

  try {
    loading.value = true
    await $fetch('/api/translations', {
      method: 'PUT',
      body: {
        id: translation.id,
        value: translation.value
      }
    })
    translation.updatedAt = new Date()

    // Clear the update status after 3 seconds
    setTimeout(() => {
      translation.updatedAt = null
    }, 3000)
  } catch (error) {
    console.error('Failed to update translation:', error)
    alert('Failed to update translation')
  } finally {
    loading.value = false
  }
}

// Handle translation change (for real-time preview updates)
function onTranslationChange(key, locale) {
  // This could trigger real-time preview updates
  console.log(`Translation changed: ${key} (${locale})`)
}

// Copy translation key to clipboard
async function copyTranslationKey(key) {
  try {
    await navigator.clipboard.writeText(key)
    // You could show a toast notification here
    console.log('Key copied to clipboard:', key)
  } catch (error) {
    console.error('Failed to copy key:', error)
  }
}

// Copy text to clipboard
async function copyToClipboard(text) {
  try {
    await navigator.clipboard.writeText(text)
    console.log('Text copied to clipboard')
  } catch (error) {
    console.error('Failed to copy text:', error)
  }
}

// Export translations as JSON
function exportTranslations() {
  const exportData = {}

  // Group translations by locale
  translations.value.forEach(t => {
    if (!exportData[t.locale]) {
      exportData[t.locale] = {}
    }
    exportData[t.locale][t.key] = t.value
  })

  // Create and download file
  const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `translations-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

// Load images
async function loadImages() {
  try {
    const response = await $fetch('/api/images/list')
    if (response.success) {
      images.value = response.images
    }
  } catch (error) {
    console.error('Failed to load images:', error)
  }
}

// Handle image upload
async function handleImageUpload(event) {
  const files = event.target.files
  if (!files || files.length === 0) return

  const formData = new FormData()
  for (let i = 0; i < files.length; i++) {
    formData.append('file', files[i])
  }

  try {
    loading.value = true
    const response = await $fetch('/api/images/upload', {
      method: 'POST',
      body: formData
    })

    if (response.success) {
      await loadImages() // Refresh the images list
      console.log('Images uploaded successfully:', response.files)
    }
  } catch (error) {
    console.error('Failed to upload images:', error)
    alert('Failed to upload images')
  } finally {
    loading.value = false
    // Clear the file input
    event.target.value = ''
  }
}

// Select image
function selectImage(image) {
  selectedImage.value = image
  // Copy image path to clipboard
  copyToClipboard(image.path)
  console.log('Selected image:', image.name)
}

// Format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Load videos
async function loadVideos() {
  try {
    const response = await $fetch('/api/videos/list')
    if (response.success) {
      videos.value = response.videos
    }
  } catch (error) {
    console.error('Failed to load videos:', error)
  }
}

// Handle video upload
async function handleVideoUpload(event) {
  const files = event.target.files
  if (!files || files.length === 0) return

  const formData = new FormData()
  for (let i = 0; i < files.length; i++) {
    formData.append('file', files[i])
  }

  try {
    loading.value = true
    const response = await $fetch('/api/videos/upload', {
      method: 'POST',
      body: formData
    })

    if (response.success) {
      await loadVideos() // Refresh the videos list
      console.log('Videos uploaded successfully:', response.files)
    }
  } catch (error) {
    console.error('Failed to upload videos:', error)
    alert('Failed to upload videos')
  } finally {
    loading.value = false
    // Clear the file input
    event.target.value = ''
  }
}

// Load data on component mount
onMounted(() => {
  loadImages()
  loadVideos()
})
</script>

<style scoped>
/* Enhanced styles for the beautiful translation management interface */
.min-h-screen {
  min-height: 100vh;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #f8fafc 100%);
}

/* Tab navigation styling */
nav button {
  position: relative;
  overflow: hidden;
}

nav button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

nav button:hover::before {
  left: 100%;
}

/* Translation group styling */
.translation-group {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.translation-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ef4444);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.translation-group:hover::before {
  opacity: 1;
}

.translation-group:hover {
  border-color: #cbd5e0;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-4px);
}

/* Preview item styling with glassmorphism */
.preview-item {
  padding: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.preview-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.preview-item:hover::before {
  left: 100%;
}

.preview-item:hover {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* Enhanced form elements with modern styling */
input, select, textarea {
  transition: all 0.3s ease;
  border-color: #e2e8f0;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

/* Button enhancements with modern effects */
button {
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: 13px;
  position: relative;
  overflow: hidden;
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Image grid styling */
.image-grid img {
  transition: transform 0.2s ease;
}

.image-grid img:hover {
  transform: scale(1.05);
}

/* Loading states */
.loading-overlay {
  position: absolute;
  inset: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* Beautiful animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
  animation: slideInFromLeft 0.5s ease-out;
}

.slide-in-right {
  animation: slideInFromRight 0.5s ease-out;
}

/* Responsive improvements with enhanced mobile experience */
@media (max-width: 768px) {
  .translation-group {
    padding: 1rem;
    border-radius: 12px;
  }

  .preview-item {
    padding: 10px;
    border-radius: 8px;
  }

  nav {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Custom scrollbar with gradient */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb 0%, #7c3aed 100%);
}

/* Enhanced highlight animations */
@keyframes highlight {
  0% {
    background: transparent;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  50% {
    background: rgba(59, 130, 246, 0.1);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    background: transparent;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.highlight-animation {
  animation: highlight 1.5s ease-in-out;
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #ef4444 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Language badge improvements */
.language-badge {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* Card improvements */
.card-container {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.card-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Search input improvements */
.search-input {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>