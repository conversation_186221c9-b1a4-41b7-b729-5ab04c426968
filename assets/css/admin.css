/* Tailwind CSS for Admin Interface Only */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Admin-specific base styles */
@layer base {
  .admin-page {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  .admin-page h1, .admin-page h2, .admin-page h3, .admin-page h4, .admin-page h5, .admin-page h6 {
    font-weight: 600;
    line-height: 1.25;
  }
  
  .admin-page input, .admin-page textarea, .admin-page select {
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
  }
  
  .admin-page input:focus, .admin-page textarea:focus, .admin-page select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/* Admin-specific components */
@layer components {
  .cms-nav-tab {
    @apply px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors duration-200 flex items-center space-x-2;
  }
  
  .cms-nav-tab-active {
    @apply bg-blue-50 text-blue-700 border border-blue-200;
  }
  
  .cms-nav-badge {
    @apply bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-medium;
  }
  
  .cms-nav-tab-active .cms-nav-badge {
    @apply bg-blue-100 text-blue-700;
  }
  
  .cms-content-section {
    @apply space-y-6;
  }
  
  .cms-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .cms-button-primary {
    @apply px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white rounded-lg font-medium hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg shadow-blue-500/25;
  }
  
  .cms-button-secondary {
    @apply px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200;
  }
  
  .cms-button-danger {
    @apply px-4 py-2 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-colors duration-200;
  }
  
  .cms-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  }
  
  .cms-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-vertical;
  }
  
  .cms-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white;
  }
  
  .cms-table {
    @apply w-full divide-y divide-gray-200;
  }
  
  .cms-table-header {
    @apply bg-gray-50 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .cms-table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .cms-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .cms-badge-green {
    @apply bg-green-100 text-green-800;
  }
  
  .cms-badge-red {
    @apply bg-red-100 text-red-800;
  }
  
  .cms-badge-blue {
    @apply bg-blue-100 text-blue-800;
  }
  
  .cms-badge-yellow {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .cms-badge-purple {
    @apply bg-purple-100 text-purple-800;
  }
}

/* Utility classes for admin */
@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
