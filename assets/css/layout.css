/* Global layout styles for consistent page spacing */

/* Ensure all pages have proper spacing below the fixed navbar */
.page-container {
  min-height: calc(100vh - 140px);
  padding: 2rem 0;
}

/* For pages without hero banners, add some top spacing */
.content-page {
  padding: 3rem 0;
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

/* Section spacing for better visual hierarchy */
.section {
  margin-bottom: 4rem;
}

.section:last-child {
  margin-bottom: 0;
}

/* Content wrapper for consistent max-width and centering */
.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Typography improvements */
.section-title {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: #1a1a1a;
  line-height: 1.2;
}

.section-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 1.5rem;
  color: #4a4a4a;
}

.section-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 1.5rem;
}

/* Card layouts */
.card-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  padding: 2rem;
  margin-bottom: 2rem;
  transition: box-shadow 0.3s ease;
}

.card-container:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Grid layouts */
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .content-wrapper {
    padding: 0 1.5rem;
  }
  
  .content-page {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-container {
    min-height: calc(100vh - 120px);
    padding: 1.5rem 0;
  }
  
  .content-page {
    padding: 2rem 0;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .content-wrapper {
    padding: 0 1rem;
  }
  
  .section {
    margin-bottom: 3rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .section-subtitle {
    font-size: 1.3rem;
  }
  
  .card-container {
    padding: 1.5rem;
  }
  
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .page-container {
    min-height: calc(100vh - 100px);
    padding: 1rem 0;
  }
  
  .content-page {
    padding: 1.5rem 0;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .content-wrapper {
    padding: 0 0.75rem;
  }
  
  .section {
    margin-bottom: 2rem;
  }
  
  .section-title {
    font-size: 1.75rem;
  }
  
  .section-subtitle {
    font-size: 1.2rem;
  }
  
  .section-text {
    font-size: 1rem;
  }
  
  .card-container {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }
.p-5 { padding: 3rem; }
